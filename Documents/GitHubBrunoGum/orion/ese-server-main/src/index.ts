// import 'dotenv/config';
import express from 'express';
import morgan from 'morgan';
import cors from 'cors';
import path from 'path';
import http from 'http';
// routes paths
import authRoute from './routes/auth/auth.route';
import userRoute from './routes/user/user.route';
import categoryRoute from './routes/category/category.route';
import productRoute from './routes/product/product.route';
import downloadRoute from './routes/download/download.route';
import manualRoute from './routes/manual/manual.route';
import messeRoute from './routes/messe/messe.route';
import agbRoute from './routes/agb/agb.route';
import datenschutzRoute from './routes/datenschutz/datenschutz.route';
import unternehmenRoute from './routes/unternehmen/unternehmen.route';
import fileRoute from './routes/file/file.route';
import contactRoute from './routes/contact/contact.route';
import impressum from './routes/impressum/impressum.route';
import csvUploader from './routes/csVUploader/csVUploader.route';

import dotenv from 'dotenv';
const app = express();
dotenv.config();
const server = http.createServer(app);

app.use('/uploads', express.static(path.join(__dirname, '..', 'uploads')));

app.use(cors({})); // todo - add origins
app.use(morgan('dev'));
app.use(express.json({ limit: '200mb' }));
app.use(express.urlencoded({ extended: false, limit: "200mb" }));
// api routes
app.use('/auth', authRoute);
app.use('/user', userRoute);
app.use('/category', categoryRoute);
app.use('/product', productRoute);
app.use('/download', downloadRoute);
app.use('/manual', manualRoute);
app.use('/messe', messeRoute);
app.use('/agb', agbRoute);
app.use('/datenschutz', datenschutzRoute);
app.use('/unternehmen', unternehmenRoute);
app.use('/file', fileRoute);
app.use('/contact', contactRoute);
app.use('/impressum', impressum);
app.use('/csvUpload', csvUploader);

app.get('/', (req, res) => {
  res.send('Work smoothly');
});

const PORT = process.env.PORT || 5005;
server.listen(PORT, () => {
  console.log(`Running on ${PORT}`);
});
