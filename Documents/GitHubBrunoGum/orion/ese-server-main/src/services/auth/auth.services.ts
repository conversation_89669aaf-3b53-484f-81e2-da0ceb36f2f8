import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';

// hashing password
const hashingPassword = async ({ password }: { password: string }) => {
  const salt = await bcrypt.genSalt(10);
  const hashedPassword = await bcrypt.hash(password, salt);
  return hashedPassword;
};

// verify password
const verifyPassword = async ({
  commingPassword,
  usersPassword,
}: {
  commingPassword: string;
  usersPassword: string;
}) => {
  const validPassword = await bcrypt.compare(commingPassword, usersPassword);
  return validPassword;
};

// assigning token
const assignToken = ({ id }: { id: number }) =>
  jwt.sign({ id }, process.env.TOKEN_SECRET as string);

export { verifyPassword, assignToken, hashingPassword };
