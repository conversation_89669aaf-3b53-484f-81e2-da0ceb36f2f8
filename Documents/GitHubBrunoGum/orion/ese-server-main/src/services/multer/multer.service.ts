const multer = require('multer');
const allowedMimeTypes = [
  // Images
  'image/png',
  'image/jpg',
  'image/jpeg',
  'image/gif',
  'image/webp',
  'image/svg+xml',
  'image/x-icon',
  'image/vnd.microsoft.icon',

  // Documents
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
  'application/vnd.ms-excel', // .xls
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
  'application/vnd.ms-powerpoint', // .ppt
  'application/vnd.openxmlformats-officedocument.presentationml.presentation', // .pptx
  'text/plain', // .txt
  'application/rtf', // .rtf
  'application/msword',
  'video/mp4',
  'text/html',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',

  // Audio
  'audio/mpeg', // .mp3
  'audio/ogg', // .ogg
  'audio/wav', // .wav

  // Video
  'video/mp4',
  'video/x-msvideo', // .avi
  'video/x-ms-wmv', // .wmv
  'video/mpeg', // .mpeg, .mpg
  'video/ogg', // .ogv
  'video/webm', // .webm
  'video/quicktime', // .mov
  'video/x-flv', // .flv
  'video/3gpp', // .3gp for mobile
  'video/3gpp2', // .3g2 for mobile
  'video/x-matroska', // .mkv
  'video/x-m4v', // .m4v
  'video/vnd.dlna.mpeg-tts', // .ts
  'video/mp2t', // .ts
  'video/vnd.uvvu.mp4', // .uvu, .uvvu
  'video/vnd.mpegurl', // .mxu, .m4u

  // Archives
  'application/zip',
  'application/x-rar-compressed',
  'application/x-7z-compressed',
  'application/x-tar',
  'application/x-bzip',
  'application/x-bzip2',
  'application/gzip',

  // Others
  'application/json', // .json
  'application/xml', // .xml
  'text/csv', // .csv
];

const storage = multer.diskStorage({
  destination: (req: any, file: any, cb: any) => {
    cb(null, 'uploads/');
  },
  filename: (req: any, file: any, cb: any) => {
    cb(null, `${Date.now()}-${file.originalname}`);
  },
});

const fileFilter = (req: any, file: any, cb: any) => {
  console.log('allowedMimeTypes.includes(file?.mimetype)',
    file?.mimetype,
    allowedMimeTypes.includes(file?.mimetype)
  );
  if (allowedMimeTypes?.includes(file?.mimetype)) {
    cb(null, true);
  } else {
    //
    cb(`Format ${file?.mimetype} not allowed`, false);
    return { message: 'Invalid type for file' };
  }
};

// const upload = multer({ storage, fileFilter });
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 200 * 1024 * 1024, // 200 MB
  },
});

export = upload;
