import { Request, Response } from 'express';
import db from '../../db';

const getAllAgb = async (req: Request, res: Response) => {
  try {
    const skip = (req?.query?.skip && +req?.query?.skip) || 0;
    const take = (req?.query?.take && +req?.query?.take) || 10;
    const search =
      typeof req?.query?.search === 'string' ? req?.query?.search : '';

    const count = await db.agb.count({
      where: {
        deleted: false,
        OR: [
          { title: { contains: search, mode: 'insensitive' } },
          { titleFr: { contains: search, mode: 'insensitive' } },
          { titleGe: { contains: search, mode: 'insensitive' } },
        ],
      },
    });
    const Agbs = await db.agb.findMany({
      where: {
        deleted: false,
        OR: [
          { title: { contains: search, mode: 'insensitive' } },
          { titleFr: { contains: search, mode: 'insensitive' } },
          { titleGe: { contains: search, mode: 'insensitive' } },
        ],
      },

      skip,
      take,
    });

    return res.status(200).json({
      Agbs,
      count,
      nextFrom: count >= take + skip ? take + skip : false,
    });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const create = async (req: Request, res: Response) => {
  try {
    const {
      title,
      titleGe,
      titleFr,
      description,
      descriptionGe,
      descriptionFr,
    }: {
      title: string;
      titleFr: string;
      titleGe: string;
      description: string;
      descriptionGe: string;
      descriptionFr: string;
    } = req.body;

    if (!title || !description) {
      return res
        .status(400)
        .json({ message: 'title and description are required!' });
    }

    await db.agb.create({
      data: {
        title,
        titleGe,
        titleFr,
        description,
        descriptionGe,
        descriptionFr,
      },
    });
    return res.status(200).json({ message: 'Agb created sucessfully!' });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const getById = async (req: Request, res: Response) => {
  try {
    const id = (req?.params?.agbId && +req?.params?.agbId) || null;

    if (!id) {
      return res.status(400).json({ message: 'Agb id is required!' });
    }

    const existAgb = await db.agb.findFirst({
      where: { id, deleted: false },
    });

    if (!existAgb) {
      return res.status(400).json({ message: 'Agb not exist!' });
    }

    return res.status(200).json({ existAgb });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const update = async (req: Request, res: Response) => {
  try {
    const id = (req?.params?.agbId && +req?.params?.agbId) || null;
    const {
      title,
      titleGe,
      titleFr,
      description,
      descriptionGe,
      descriptionFr,
    } = req.body;

    if (!id) {
      return res.status(400).json({ message: 'Agb id is required!' });
    }

    const existAgb = await db.agb.findFirst({
      where: { id, deleted: false },
    });

    if (!existAgb) {
      return res.status(400).json({ message: 'Agb not exist!' });
    }

    const update = await db.agb.update({
      where: { id: existAgb?.id },
      data: {
        title,
        titleGe,
        titleFr,
        description,
        descriptionGe,
        descriptionFr,
      },
    });
    return res.status(200).json({ message: 'Agb updated sucessfully!' });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const deleteAgb = async (req: Request, res: Response) => {
  try {
    const id = (req?.params?.agbId && +req?.params?.agbId) || null;

    if (!id) {
      return res.status(400).json({ message: 'Agb id is required!' });
    }

    const existAgb = await db.agb.findFirst({
      where: { id, deleted: false },
    });

    if (!existAgb) {
      return res.status(400).json({ message: 'Agb not exist!' });
    }

    const update = await db.agb.update({
      where: { id: existAgb?.id },
      data: { deleted: true },
    });
    return res.status(200).json({ message: 'Agb deleted sucessfully!' });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

export { getAllAgb, create, getById, update, deleteAgb };
