import { Request, Response } from 'express';
import db from '../../db';

const getUsers = async (req: Request, res: Response) => {
  try {
    const skip = (req?.query?.skip && +req?.query?.skip) || 0;
    const take = (req?.query?.take && +req?.query?.take) || 10;
    const search =
      typeof req?.query?.search === 'string' ? req?.query?.search : '';
    const count = await db.user.count({
      where: {
        deleted: false,
        OR: [
          { email: { contains: search, mode: 'insensitive' } },
          { username: { contains: search, mode: 'insensitive' } },
        ],
      },
    });
    const users = await db.user.findMany({
      where: {
        deleted: false,
        OR: [
          { email: { contains: search, mode: 'insensitive' } },
          { username: { contains: search, mode: 'insensitive' } },
        ],
      },
      select: { id: true, email: true, username: true, role: true },
      skip,
      take,
    });

    return res.status(200).json({
      users,
      count,
      nextFrom: count >= take + skip ? take + skip : false,
    });
  } catch (error) {
    console.log('error in change password', error);
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};
export { getUsers };
