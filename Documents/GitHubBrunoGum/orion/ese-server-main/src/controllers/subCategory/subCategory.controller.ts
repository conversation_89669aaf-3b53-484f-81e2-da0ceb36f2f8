import { Request, Response } from 'express';
import db from '../../db';

const getAllSubCategories = async (req: Request, res: Response) => {
  try {
    const skip = (req?.query?.skip && +req?.query?.skip) || undefined;
    const take = (req?.query?.take && +req?.query?.take) || undefined;
    const search =
      typeof req?.query?.search === 'string' ? req?.query?.search : '';
    const count = await db.subCategory.count({
      where: {
        deleted: false,
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { nameGe: { contains: search, mode: 'insensitive' } },
          { nameFr: { contains: search, mode: 'insensitive' } },
        ],
      },
    });
    const subCategories = await db.subCategory.findMany({
      where: {
        deleted: false,
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { nameGe: { contains: search, mode: 'insensitive' } },
          { nameFr: { contains: search, mode: 'insensitive' } },
        ],
      },
      orderBy: { subCategoryNumber: 'asc' },
      include: {
        Category: true,
        Product: {
          orderBy: { productNumber: 'asc' },
          include: { Pdf: true, Files: true },
        },
      },
      skip,
      take,
    });

    return res.status(200).json({
      subCategories,
      count,
      ...(take &&
        skip && { nextFrom: count >= take + skip ? take + skip : false }),
    });
  } catch (error) {
    console.log('error in change password', error);
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const subCategoryCreate = async (req: Request, res: Response) => {
  try {
    const { name, nameGe, nameFr, subCategoryNumber, categoryId } = req.body;

    if (!name) {
      return res
        .status(400)
        .json({ message: 'sub Category name is required!' });
    }

    const categoryExist = await db.category.findFirst({
      where: {
        id: +categoryId,
        deleted: false,
      },
    });

    if (!categoryExist) {
      return res.status(400).json({
        message: `category not exist!`,
      });
    }
    const existSubCategory = await db.subCategory.findFirst({
      where: {
        categoryId: +categoryId,
        deleted: false,
        OR: [{ name }, { nameGe }, { nameFr }],
      },
    });

    if (existSubCategory) {
      return res
        .status(400)
        .json({ message: 'sub Category already exist in category!' });
    }
    const create = await db.subCategory.create({
      data: {
        name,
        nameGe,
        nameFr,
        subCategoryNumber: +subCategoryNumber,
        Category: { connect: { id: +categoryId } },
      },
    });
    return res
      .status(200)
      .json({ message: 'sub Category created sucessfully!' });
  } catch (error) {
    console.log('error in change password', error);
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const getByIdSubCategory = async (req: Request, res: Response) => {
  try {
    const id =
      (req?.params?.subCategoryId && +req?.params?.subCategoryId) || null;

    if (!id) {
      return res.status(400).json({ message: 'Sub Category id is required!' });
    }

    const existCategory = await db.subCategory.findFirst({
      where: { id, deleted: false },
      include: {
        Category: true,
      },
    });

    if (!existCategory) {
      return res.status(400).json({ message: 'Category not exist!' });
    }

    return res.status(200).json({ existCategory });
  } catch (error) {
    console.log('error in change password', error);
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const updateSubCategory = async (req: Request, res: Response) => {
  try {
    const id =
      (req?.params?.subCategoryId && +req?.params?.subCategoryId) || null;
    const { name, nameGe, nameFr, subCategoryNumber, categoryId } = req.body;
    console.log('name-->', name, nameGe, nameFr, subCategoryNumber);

    if (!id) {
      return res.status(400).json({ message: 'sub Category id is required!' });
    }
    const subCategory = await db.subCategory.findFirst({
      where: {
        id: +id,
        deleted: false,
      },
    });

    console.log('subCategory-->', subCategory);

    if (!subCategory) {
      return res.status(400).json({
        message: 'sub Category is exist  not!',
      });
    }

    const update = await db.subCategory.update({
      where: { id: +id },
      data: {
        name,
        nameGe,
        nameFr,
        subCategoryNumber: +subCategoryNumber,
        Category: { connect: { id: +categoryId } },
      },
    });
    return res
      .status(200)
      .json({ message: 'sub Category updated sucessfully!' });
  } catch (error) {
    console.log('error in change password', error);
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const deleteSubCategory = async (req: Request, res: Response) => {
  try {
    const id =
      (req?.params?.subCategoryId && +req?.params?.subCategoryId) || null;

    if (!id) {
      return res.status(400).json({ message: 'Category id is required!' });
    }

    const existCategory = await db.subCategory.findFirst({
      where: { id, deleted: false },
      include: { Product: { where: { deleted: false } } },
    });

    if (existCategory?.Product && existCategory?.Product?.length > 0) {
      return res
        .status(400)
        .json({ message: 'This sub Category have Products!' });
    }
    if (!existCategory) {
      return res.status(400).json({ message: 'sub Category not exist!' });
    }

    const update = await db.subCategory.update({
      where: { id: existCategory?.id },
      data: { deleted: true },
    });
    return res
      .status(200)
      .json({ message: 'Sub Category deleted sucessfully!' });
  } catch (error) {
    console.log('error in change password', error);
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

export {
  getAllSubCategories,
  subCategoryCreate,
  getByIdSubCategory,
  updateSubCategory,
  deleteSubCategory,
};
