import { Request, Response } from 'express';
import db from '../../db';
import { removeFiles } from '../../utils/file/file.utils';

const fileUpdate = async (req: Request, res: Response) => {
  try {
    const { fileId } = req?.params;
    console.log('file?>>>>>>>>>');
    if (!fileId) {
      return res.status(400).json({
        message: 'File id is required!',
      });
    }
    const { file } = req;
    const getFileById = await db.file.findFirst({
      where: {
        id: +fileId,
      },
    });

    if (getFileById) {
      // to remove the file from upload folder
      removeFiles(getFileById);
      await db.file.update({
        where: {
          id: +getFileById.id,
        },
        data: {
          mimeType: file?.mimetype,
          url: file?.path,
        },
      });
      return res.status(200).json({
        message: ' File updated successfully!',
      });
    } else {
      return res.status(400).json({ message: 'File is not exist!' });
    }
  } catch (err) {
    console.log('Error', err);
    return res
      .status(500)
      .json({ message: 'Internal server error!', error: err });
  }
};

const fileDelete = async (req: Request, res: Response) => {
  try {
    const { fileId } = req?.params;

    if (!fileId) {
      return res.status(400).json({
        message: 'File id is required!',
      });
    }
    const getFileById = await db.file.findFirst({
      where: {
        id: +fileId,
      },
    });

    if (getFileById) {
      // to remove the file from upload folder
      removeFiles(getFileById);
      await db.file.update({
        where: {
          id: +getFileById.id,
        },
        data: {
          deleted: true,
        },
      });
      return res.status(200).json({
        message: ' File deleted successfully!',
      });
    } else {
      return res.status(400).json({ message: 'File is not exist!' });
    }
  } catch (err) {
    console.log('Error', err);
    return res
      .status(500)
      .json({ message: 'Internal server error!', error: err });
  }
};

const File = async (req: Request, res: Response) => {
  try {
    const { file } = req;
    console.log('this is called file>>>>>>>>>>>>>>>', file);
    if (!file?.path) {
      return res.status(400).json({ message: 'Invalid file!' });
    }

    return res.status(200).json({
      data: { link: `${process.env.BACKEND_BASEURL}/${file?.path}` },
      url: file.path,
      mimeType: file.mimetype,
    });
  } catch (error) {
    return res.status(500).json({ message: 'Internal Server Error!', error });
  }
};

export { fileUpdate, File, fileDelete };
