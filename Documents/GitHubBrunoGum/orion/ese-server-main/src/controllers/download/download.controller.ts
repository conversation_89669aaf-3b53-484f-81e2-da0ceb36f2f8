import { Request, Response } from 'express';
import db from '../../db';
import { removeFiles } from '../../utils/file/file.utils';

const getAllDownloads = async (req: Request, res: Response) => {
  try {
    const skip = (req?.query?.skip && +req?.query?.skip) || 0;
    const take = (req?.query?.take && +req?.query?.take) || 10;
    const search =
      typeof req?.query?.search === 'string' ? req?.query?.search : '';

    const count = await db.downloads.count({
      where: {
        deleted: false,
        OR: [
          { title: { contains: search, mode: 'insensitive' } },
          { titleFr: { contains: search, mode: 'insensitive' } },
          { titleGe: { contains: search, mode: 'insensitive' } },
        ],
      },
    });
    const downloads = await db.downloads.findMany({
      where: {
        deleted: false,
        OR: [
          { title: { contains: search, mode: 'insensitive' } },
          { titleFr: { contains: search, mode: 'insensitive' } },
          { titleGe: { contains: search, mode: 'insensitive' } },
        ],
      },
      include: {
        Files: {
          where: { deleted: false },
          select: { id: true, url: true, mimeType: true },
        },
      },
      skip,
      take,
    });

    return res.status(200).json({
      downloads,
      count,
      nextFrom: count >= take + skip ? take + skip : false,
    });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const create = async (req: Request, res: Response) => {
  try {
    const files: Express.Multer.File[] | undefined =
      req.files as Express.Multer.File[];
    console.log('files', files);
    const {
      title,
      titleGe,
      titleFr,
    }: { title: string; titleGe: string; titleFr: string } = req.body;

    if (!title) {
      return res.status(400).json({ message: 'title is required!' });
    }

    const create = await db.downloads.create({
      data: {
        title,
        titleGe,
        titleFr,
        ...(files &&
          files?.length > 0 && {
            Files: {
              create: files?.map((file: any) => ({
                url: file?.path,
                mimeType: file?.mimetype,
              })),
            },
          }),
      },
      include: { Files: { select: { id: true, url: true, mimeType: true } } },
    });
    return res.status(200).json({ message: 'Download created sucessfully!' });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const getById = async (req: Request, res: Response) => {
  try {
    const id = (req?.params?.downloadId && +req?.params?.downloadId) || null;

    if (!id) {
      return res.status(400).json({ message: 'Download id is required!' });
    }

    const existDownloads = await db.downloads.findFirst({
      where: { id, deleted: false },
      include: {
        Files: {
          where: { deleted: false },
          select: { id: true, url: true, mimeType: true },
        },
      },
    });

    if (!existDownloads) {
      return res.status(400).json({ message: 'Download not exist!' });
    }

    return res.status(200).json({ existDownloads });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const update = async (req: Request, res: Response) => {
  try {
    const id = (req?.params?.downloadId && +req?.params?.downloadId) || null;
    const {
      title,
      titleGe,
      titleFr,
    }: { title: string; titleGe: string; titleFr: string } = req.body;
    const Images: any = req?.files;
    if (!id) {
      return res.status(400).json({ message: 'Download id is required!' });
    }

    const existDownloads = await db.downloads.findFirst({
      where: { id, deleted: false },
      include: { Files: true },
    });

    if (!existDownloads) {
      return res.status(400).json({ message: 'Download not exist!' });
    }

    await db.downloads.update({
      where: { id: existDownloads?.id },
      data: {
        title,
        titleGe,
        titleFr,
        ...(Images &&
          Images.length > 0 && {
            Files: {
              create: Images?.map((file: any) => ({
                url: file.path,
                mimeType: file.mimetype,
              })),
            },
          }),
      },
    });
    return res.status(200).json({ message: 'Download updated sucessfully!' });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const deleteProduct = async (req: Request, res: Response) => {
  try {
    const id = (req?.params?.downloadId && +req?.params?.downloadId) || null;

    if (!id) {
      return res.status(400).json({ message: 'Download id is required!' });
    }

    const existDownloads = await db.downloads.findFirst({
      where: { id, deleted: false },
      include: {
        Files: {
          where: { deleted: false },
        },
      },
    });

    if (!existDownloads) {
      return res.status(400).json({ message: 'Download not exist!' });
    }
    existDownloads?.Files?.map((file: any) => {
      removeFiles(file);
    });
    const update = await db.downloads.update({
      where: { id: existDownloads?.id },
      data: { deleted: true },
    });
    return res.status(200).json({ message: 'Download deleted sucessfully!' });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

export { getAllDownloads, create, getById, update, deleteProduct };
