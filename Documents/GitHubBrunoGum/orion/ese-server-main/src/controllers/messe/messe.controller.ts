// @ts-nocheck
import { Request, Response } from 'express';
import db from '../../db';
import { removeFiles } from '../../utils/file/file.utils';

const getAllMesse = async (req: Request, res: Response) => {
  try {
    const skip = (req?.query?.skip && +req?.query?.skip) || 0;
    const take = (req?.query?.take && +req?.query?.take) || 10;
    const search =
      typeof req?.query?.search === 'string' ? req?.query?.search : '';

    const count = await db.messe.count({
      where: {
        deleted: false,
        OR: [
          { title: { contains: search, mode: 'insensitive' } },
          { titleFr: { contains: search, mode: 'insensitive' } },
          { titleGe: { contains: search, mode: 'insensitive' } },
        ],
      },
    });
    const manuals = await db.messe.findMany({
      where: {
        deleted: false,
        OR: [
          { title: { contains: search, mode: 'insensitive' } },
          { titleFr: { contains: search, mode: 'insensitive' } },
          { titleGe: { contains: search, mode: 'insensitive' } },
        ],
      },
      skip,
      take,
    });

    return res.status(200).json({
      manuals,
      count,
      nextFrom: count >= take + skip ? take + skip : false,
    });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const create = async (req: Request, res: Response) => {
  try {
    const {
      title,
      titleGe,
      titleFr,
      description,
      descriptionGe,
      descriptionFr,
    }: {
      title: string;
      titleFr: string;
      titleGe: string;
      description: string;
      descriptionGe: string;
      descriptionFr: string;
    } = req.body;

    if (!title || !description) {
      return res
        .status(400)
        .json({ message: 'title and description are required!' });
    }

    await db.messe.create({
      data: {
        title,
        titleGe,
        titleFr,
        description,
        descriptionGe,
        descriptionFr,
      },
    });
    return res
      .status(200)
      .json({ message: 'Messe created sucessfully!' });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const getById = async (req: Request, res: Response) => {
  try {
    const id = (req?.params?.messeId && +req?.params?.messeId) || null;

    if (!id) {
      return res.status(400).json({ message: 'Messe id is required!' });
    }

    const existMesse = await db.messe.findFirst({
      where: { id, deleted: false },
    });

    if (!existMesse) {
      return res.status(400).json({ message: 'Messe not exist!' });
    }

    return res.status(200).json({ existMesse });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const update = async (req: Request, res: Response) => {
  try {
    const id =
      (req?.params?.messeId && +req?.params?.messeId) || null;
    const {
      title,
      titleGe,
      titleFr,
      description,
      descriptionGe,
      descriptionFr,
    } = req.body;

    if (!id) {
      return res.status(400).json({ message: 'Messe id is required!' });
    }

    const existMesse = await db.messe.findFirst({
      where: { id, deleted: false },
    });

    if (!existMesse) {
      return res.status(400).json({ message: 'Messe not exist!' });
    }

    const update = await db.messe.update({
      where: { id: existMesse?.id },
      data: {
        title,
        titleGe,
        titleFr,
        description,
        descriptionGe,
        descriptionFr,
      },
    });
    return res
      .status(200)
      .json({ message: 'Messe updated sucessfully!' });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const deleteMesse = async (req: Request, res: Response) => {
  try {
    const id = (req?.params?.messeId && +req?.params?.messeId) || null;

    if (!id) {
      return res.status(400).json({ message: 'Messe id is required!' });
    }

    const existMesse = await db.messe.findFirst({
      where: { id, deleted: false },
    });

    if (!existMesse) {
      return res.status(400).json({ message: 'Messe not exist!' });
    }

    const update = await db.messe.update({
      where: { id: existMesse?.id },
      data: { deleted: true },
    });
    return res.status(200).json({ message: 'Messe deleted sucessfully!' });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

export { getAllMesse, create, getById, update, deleteMesse };
