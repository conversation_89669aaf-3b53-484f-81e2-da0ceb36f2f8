import { Request, Response } from 'express';
import db from '../../db';

import { assignToken, verifyPassword } from '../../services/auth/auth.services';
import decryptPassword from '../../utils/password/password.utils';

const login = async (req: Request, res: Response) => {
  try {
    const { email, password: encryptedPassword } = req?.body;
    const password = decryptPassword({ encryptedPassword });

    if (!password) {
      return res
        .status(400)
        .json({ message: 'Secret Key not found at backend!' });
    }

    const user = await db.user.findFirst({
      where: {
        email,
        deleted: false,
      },
    });

    if (!user) {
      return res.status(400).json({ message: 'invalid email or password' });
    }

    //   checking the password
    const validPassword = await verifyPassword({
      commingPassword: password,
      usersPassword: user.password,
    });

    if (!validPassword)
      return res.status(400).json({
        message: 'invalid email or password',
      });

    // creating token and assigning
    const token = assignToken({ id: user?.id });
    const response = {
      token,
      User: {
        id: user?.id,
        email: user?.email,
        username: user?.username,
      },
    };

    return res.status(200).json({ message: 'login sucessfully!', response });
  } catch (error) {
    console.log('error', error);
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

export { login };
