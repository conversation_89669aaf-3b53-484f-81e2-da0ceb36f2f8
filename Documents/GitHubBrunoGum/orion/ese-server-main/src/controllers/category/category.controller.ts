import { Request, Response } from 'express';
import db from '../../db';
import { paginate } from '../../utils/pageinate/pageinate';

const getAllCategories = async (req: Request, res: Response) => {
  try {
    const skip = (req?.query?.skip && +req?.query?.skip) || 0;
    const take = (req?.query?.take && +req?.query?.take) || 10;
    const search =
      typeof req?.query?.search === 'string' ? req?.query?.search : '';
    const count = await db.category.count({
      where: {
        deleted: false,
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { nameGe: { contains: search, mode: 'insensitive' } },
          { nameFr: { contains: search, mode: 'insensitive' } },
        ],
      },
    });
    const categories = await db.category.findMany({
      where: {
        deleted: false,
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { nameGe: { contains: search, mode: 'insensitive' } },
          { nameFr: { contains: search, mode: 'insensitive' } },
        ],
      },
      orderBy: { categoryNumber: 'asc' },
      include: {
        Product: {
          orderBy: { productNumber: 'asc' },
          include: { Pdf: true, Files: true },
        },
      },
      skip,
      take,
    });

    return res.status(200).json({
      categories,
      count,
      nextFrom: count >= take + skip ? take + skip : false,
    });
  } catch (error) {
    console.log('error in change password', error);
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const create = async (req: Request, res: Response) => {
  try {
    const { name, nameGe, nameFr, categoryNumber } = req.body;

    if (!name) {
      return res.status(400).json({ message: 'Category name is required!' });
    }
    const existCategory = await db.category.findFirst({
      where: { deleted: false, OR: [{ name }, { nameGe }, { nameFr }] },
    });
    if (existCategory) {
      return res.status(400).json({ message: 'Category already exist!' });
    }
    await db.category.create({
      data: {
        name,

        nameGe,
        nameFr,
        categoryNumber: +categoryNumber,
      },
    });
    return res.status(200).json({ message: 'Category created sucessfully!' });
  } catch (error) {
    console.log('error in change password', error);
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const getById = async (req: Request, res: Response) => {
  try {
    const id = (req?.params?.catedoryId && +req?.params?.catedoryId) || null;

    if (!id) {
      return res.status(400).json({ message: 'Category id is required!' });
    }

    const existCategory = await db.category.findFirst({
      where: { id, deleted: false },
      include: {
        Product: {
          orderBy: { productNumber: 'asc' },
          include: { Pdf: true, Files: true },
        },
      },
    });

    if (!existCategory) {
      return res.status(400).json({ message: 'Category not exist!' });
    }

    return res.status(200).json({ existCategory });
  } catch (error) {
    console.log('error in change password', error);
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const update = async (req: Request, res: Response) => {
  try {
    const id = (req?.params?.catedoryId && +req?.params?.catedoryId) || null;
    const { name, nameGe, nameFr, categoryNumber, bigCategory } = req.body;
    console.log('name-->', name, nameGe, nameFr, categoryNumber);

    if (!id) {
      return res.status(400).json({ message: 'Category id is required!' });
    }
    const sameCategory = await db.category.findFirst({
      where: { OR: [{ name }, { nameGe }, { nameFr }], deleted: false },
    });

    const existCategory = await db.category.findFirst({
      where: { id, deleted: false },
    });

    // console.log("existCategory-->",existCategory)
    if (
      existCategory?.id &&
      sameCategory?.id &&
      sameCategory?.id !== existCategory?.id
    ) {
      return res
        .status(400)
        .json({ message: 'Category with this name already exist!' });
    }

    if (!existCategory) {
      return res.status(400).json({ message: 'Category not exist!' });
    }

    await db.category.update({
      where: { id: existCategory?.id },
      data: {
        name,
        nameGe,
        nameFr,
        categoryNumber: +categoryNumber,
      },
    });
    return res.status(200).json({ message: 'Category updated sucessfully!' });
  } catch (error) {
    console.log('error in change password', error);
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const deleteCategory = async (req: Request, res: Response) => {
  try {
    const id = (req?.params?.catedoryId && +req?.params?.catedoryId) || null;

    if (!id) {
      return res.status(400).json({ message: 'Category id is required!' });
    }

    const existCategory = await db.category.findFirst({
      where: { id, deleted: false },
      include: { Product: { where: { deleted: false } } },
    });

    if (existCategory?.Product && existCategory?.Product?.length > 0) {
      return res.status(400).json({ message: 'This Category have Products!' });
    }
    if (!existCategory) {
      return res.status(400).json({ message: 'Category not exist!' });
    }

    const update = await db.category.update({
      where: { id: existCategory?.id },
      data: { deleted: true },
    });
    return res.status(200).json({ message: 'Category deleted sucessfully!' });
  } catch (error) {
    console.log('error in change password', error);
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const getAllCategoriesOrProduct = async (req: Request, res: Response) => {
  try {
    const skip = (req?.query?.skip && +req?.query?.skip) || 0;
    const take = (req?.query?.take && +req?.query?.take) || undefined;
    const search =
      typeof req?.query?.search === 'string' ? req?.query?.search : '';

    const categoriesWithProducts = await db.category.findMany({
      where: {
        deleted: false,
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { nameGe: { contains: search, mode: 'insensitive' } },
          { nameFr: { contains: search, mode: 'insensitive' } },
        ],
      },
      orderBy: { categoryNumber: 'asc' },
      include: {
        SubCategory: {
          where: {
            deleted: false,
          },
          orderBy: { subCategoryNumber: 'asc' },
          select: {
            id: true,
            name: true,
            nameGe: true,
            nameFr: true,
            Product: {
              orderBy: { productNumber: 'asc' },
              where: { deleted: false },
              select: {
                id: true,
                title: true,
                titleFr: true,
                titleGe: true,
                productNumber: true,
              },
            },
          },
        },
        Product: {
          where: {
            deleted: false,
            subCategoryId: null,
          },
          select: {
            id: true,
            title: true,
            titleGe: true,
            titleFr: true,
            productNumber: true,
          },
        },
      },
    });

    const productsWithoutCategory = await db.product.findMany({
      where: {
        categoryId: null,
        subCategoryId: null,
        deleted: false,
      },
      orderBy: { productNumber: 'asc' },
      select: {
        id: true,
        title: true,
        titleGe: true,
        titleFr: true,
        productNumber: true,
      },
    });

    const items = [...categoriesWithProducts, ...productsWithoutCategory];

    const categoryOrProduct = take ? paginate(items, skip, take) : items;

    const count = items.length;

    console.log('categoryOrProduct===>', categoryOrProduct);

    return res.status(200).json({
      categoryOrProduct,
      count,
      ...(take && { nextFrom: count >= take + skip ? take + skip : false }),
    });
  } catch (error) {
    console.log('error in getAllCategoriesOrProduct', error);
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

export {
  getAllCategories,
  create,
  getById,
  update,
  deleteCategory,
  getAllCategoriesOrProduct,
};
