import { Request, Response } from 'express';
import ExcelJS from 'exceljs';
import path from 'path';
import fs from 'fs';
import axios from 'axios';
import { PrismaClient } from '@prisma/client';
import db from '../../db';

const prisma = new PrismaClient();

// const downloadAndSaveImage = async (imageUrl: string, res: Response) => {
//   const uploadDir = path.join(__dirname, '../../../uploads');
//   console.log('uploadDir===>>>', uploadDir);
//   console.log('imageUrl===>>>', imageUrl);

//   try {
//     // Ensure the uploads directory exists, create it if it doesn't
//     if (!fs.existsSync(uploadDir)) {
//       console.log('Uploads directory not found, creating...');
//       fs.mkdirSync(uploadDir, { recursive: true });
//     }

//     // Download the image
//     const response = await axios.get(imageUrl, { responseType: 'arraybuffer' });
//     const buffer = Buffer.from(response.data, 'binary');

//     // Determine MIME type
//     const mimeType = response.headers['content-type'];

//     // Generate a unique filename (e.g., using timestamp)
//     const timestamp = Date.now();
//     const extension = mimeType.split('/')[1]; // Get file extension from MIME type
//     const filename = `image_${timestamp}.${extension}`;
//     const filepath = path.join(uploadDir, filename);

//     // Save the image to the uploads directory
//     fs.writeFileSync(filepath, buffer);
//     console.log('Image saved:', filepath); // Log the saved path

//     // Construct URL for the saved image
//     const savedImageUrl = `uploads/${filename}`; // Adjust BASE_URL as per your setup

//     return { filepath, mimeType, savedImageUrl };
//   } catch (error) {
//     console.error('Error downloading or saving image:', error);
//     return res.status(400).json({
//       message: 'Error downloading or saving image',
//       error: error,
//     });
//   }
// };

const downloadAndSaveFile = async (fileUrl: string, res: Response) => {
  // Log the URL to debug
  console.log('Downloading file from:', fileUrl);

  const uploadDir = path.join(__dirname, '../../../uploads');
  console.log('uploadDir===>>>', uploadDir);

  try {
    // Ensure the uploads directory exists, create it if it doesn't
    if (!fs.existsSync(uploadDir)) {
      console.log('Uploads directory not found, creating...');
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    // Download the file
    const response = await axios.get(fileUrl, { responseType: 'arraybuffer' });
    const buffer = Buffer.from(response.data, 'binary');
    const mimeType = response.headers['content-type'];
    const timestamp = Date.now();
    let extension = mimeType.split('/')[1];

    if (extension === 'jpeg') extension = 'jpg';
    if (extension === 'plain') extension = 'txt';
    if (mimeType.includes('pdf')) extension = 'pdf';

    const filename = `file_${timestamp}.${extension}`;
    const filepath = path.join(uploadDir, filename);

    fs.writeFileSync(filepath, buffer);
    console.log('File saved:', filepath);

    const savedFileUrl = `uploads/${filename}`;
    return { filepath, mimeType, savedFileUrl };
  } catch (error) {
    console.error('Error downloading or saving file:', error);
    return res.status(400).json({
      message: 'Error downloading or saving file',
      error: error,
    });
  }
};

const csVUploader = async (req: Request, res: Response) => {
  try {
    const file = req.file;
    if (!file) {
      return res.status(400).json({ message: 'file is required!' });
    }
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(file.path);
    const worksheet = workbook.worksheets[0];

    worksheet?.eachRow(async (row: any, rowNumber: any) => {
      if (rowNumber > 1) {
        // Skipping the header row
        const titleEn = `${row.getCell(1).value}`;
        const titleGe = `${row.getCell(2).value}`;
        const titleFr = `${row.getCell(3).value}`;
        const category = `${row.getCell(4).value}`;
        const subCategory = `${row.getCell(5).value}`;
        const bigCategory = `${row.getCell(6).value}`;
        const productNumber: any = row.getCell(7).value;
        const descriptionEn = `${row.getCell(8).value}`;
        const descriptionGe = `${row.getCell(9).value}`;
        const descriptionFr = `${row.getCell(10).value}`;
        const moreDetailEn = `${row.getCell(11).value}`;
        const moreDetailGe = `${row.getCell(12).value}`;
        const moreDetailFr = `${row.getCell(13).value}`;
        const imageLink: any = row.getCell(14).value;
        const fileLink: any = row.getCell(15).value;
        const imageLinksArray = [];
        const imageLinks = imageLink?.text?.split(' ');

        const filteredUrls = imageLinks?.filter((url: any) => url !== '');

        let existCategory = null;
        let existBigCategory = null;
        let existSubCategory = null;
        if (category) {
          existCategory = await db.category.findFirst({
            where: { deleted: false, name: category },
          });
        }
        if (bigCategory) {
          existBigCategory = await db.bigCategory.findFirst({
            where: { deleted: false, name: bigCategory },
          });
        }
        if (subCategory) {
          existSubCategory = await db.subCategory.findFirst({
            where: { deleted: false, name: subCategory },
          });
        }
        let fileResult: any = null;
        if (fileLink?.text) {
          fileResult = await downloadAndSaveFile(fileLink?.text, res);
        }
        const imagesResult = [];
        for (const url of filteredUrls) {
          const result: any = await downloadAndSaveFile(url, res);
          imagesResult.push(result);
        }
        console.log('imagesResult>>>>>>>>>>>>>>>>>>', imagesResult);
        if (imagesResult?.length > 0) {
          const result: any = await downloadAndSaveFile(imageLink.text, res);

          await db.product.create({
            data: {
              title: titleEn,
              titleFr,
              titleGe,
              moreDetail: moreDetailEn,
              moreDetailFr,
              moreDetailGe,
              description: descriptionEn,
              descriptionFr,
              descriptionGe,
              productNumber: productNumber && +productNumber,
              ...(existBigCategory && {
                bigCategory: { connect: { id: existBigCategory?.id } },
              }),
              ...(existSubCategory && {
                SubCategory: { connect: { id: existSubCategory?.id } },
              }),
              ...(existCategory && {
                Category: { connect: { id: existCategory?.id } },
              }),

              Files: {
                create: {
                  mimeType: result.mimeType,
                  url: result.savedFileUrl,
                },
              },
              ...(fileResult?.mimeType && {
                Pdf: {
                  create: {
                    mimeType: fileResult?.mimeType,
                    url: fileResult?.savedFileUrl,
                  },
                },
              }),
            },
          });
        } else {
          await db.product.create({
            data: {
              title: titleEn,
              titleFr,
              titleGe,
              moreDetail: moreDetailEn,
              moreDetailFr,
              moreDetailGe,
              description: descriptionEn,
              descriptionFr,
              descriptionGe,
              productNumber: productNumber && +productNumber,
              ...(existBigCategory && {
                bigCategory: { connect: { id: existBigCategory?.id } },
              }),
              ...(existSubCategory && {
                SubCategory: { connect: { id: existSubCategory?.id } },
              }),
              ...(existCategory && {
                Category: { connect: { id: existCategory?.id } },
              }),
              ...(fileResult && {
                Pdf: {
                  create: {
                    mimeType: fileResult?.mimeType,
                    url: fileResult?.savedFileUrl,
                  },
                },
              }),
            },
          });
        }
      }
    });

    fs.unlinkSync(file.path); // Cleanup uploaded file
    return res
      .status(200)
      .json({ message: 'File processed and data inserted successfully!' });
  } catch (error) {
    console.error('Error:', error);
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

// const csVUploader = async (req: Request, res: Response) => {
//   try {
//     const file = req.file;
//     if (!file) {
//       return res.status(400).json({ message: 'No file uploaded!' });
//     }
//     const workbook = new ExcelJS.Workbook();
//     await workbook.xlsx.readFile(file.path);
//     const worksheet = workbook.worksheets[0];

//     for (const row of worksheet.rows) {
//       if (row.number > 1) {
//         // Skipping the header row
//         const titleEn = row.getCell(1).value;
//         const titleGe = row.getCell(2).value;
//         const titleFr = row.getCell(3).value;
//         const descriptionEn = row.getCell(4).value;
//         const descriptionGe = row.getCell(5).value;
//         const descriptionFr = row.getCell(6).value;
//         const moreDetailEn = row.getCell(7).value;
//         const moreDetailGe = row.getCell(8).value;
//         const moreDetailFr = row.getCell(9).value;
//         const imageLink = row.getCell(10).value;

//         if (imageLink) {
//           const result = await downloadAndSaveImage(imageLink.toString(), res);
//           console.log('result==>>', result);
//         }

//         // Here add your database insertion logic
//         console.log(`Row ${row.number} data:`, {
//           titleEn,
//           titleGe,
//           titleFr,
//           descriptionEn,
//           descriptionGe,
//           descriptionFr,
//           moreDetailEn,
//           moreDetailGe,
//           moreDetailFr,
//           imageLink,
//         });
//       }
//     }

//     fs.unlinkSync(file.path); // Cleanup uploaded file
//     return res
//       .status(200)
//       .json({ message: 'File processed and data inserted successfully!' });
//   } catch (error) {
//     console.error('Error:', error);
//     return res.status(500).json({ message: 'Internal server error!', error });
//   }
// };

export { csVUploader };
