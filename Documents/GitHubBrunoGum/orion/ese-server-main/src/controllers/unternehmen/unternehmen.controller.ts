import { Request, Response } from 'express';
import db from '../../db';

const getAllUnternehmen = async (req: Request, res: Response) => {
  try {
    const skip = (req?.query?.skip && +req?.query?.skip) || 0;
    const take = (req?.query?.take && +req?.query?.take) || 10;
    const search =
      typeof req?.query?.search === 'string' ? req?.query?.search : '';

    const count = await db.unternehmen.count({
      where: {
        deleted: false,
        OR: [
          { title: { contains: search, mode: 'insensitive' } },
          { titleFr: { contains: search, mode: 'insensitive' } },
          { titleGe: { contains: search, mode: 'insensitive' } },
        ],
      },
    });
    const Unternehmens = await db.unternehmen.findMany({
      where: {
        deleted: false,
        OR: [
          { title: { contains: search, mode: 'insensitive' } },
          { titleFr: { contains: search, mode: 'insensitive' } },
          { titleGe: { contains: search, mode: 'insensitive' } },
        ],
      },

      skip,
      take,
    });

    return res.status(200).json({
      Unternehmens,
      count,
      nextFrom: count >= take + skip ? take + skip : false,
    });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const create = async (req: Request, res: Response) => {
  try {
    const {
      title,
      titleGe,
      titleFr,
      description,
      descriptionGe,
      descriptionFr,
    }: {
      title: string;
      titleFr: string;
      titleGe: string;
      description: string;
      descriptionGe: string;
      descriptionFr: string;
    } = req.body;

    if (!title || !description) {
      return res
        .status(400)
        .json({ message: 'title and description are required!' });
    }

    await db.unternehmen.create({
      data: {
        title,
        titleGe,
        titleFr,
        description,
        descriptionGe,
        descriptionFr,
      },
    });
    return res
      .status(200)
      .json({ message: 'Unternehmen created sucessfully!' });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const getById = async (req: Request, res: Response) => {
  try {
    const id =
      (req?.params?.unternehmenId && +req?.params?.unternehmenId) || null;

    if (!id) {
      return res.status(400).json({ message: 'Unternehmen id is required!' });
    }

    const existUnternehmen = await db.unternehmen.findFirst({
      where: { id, deleted: false },
    });

    if (!existUnternehmen) {
      return res.status(400).json({ message: 'Unternehmen not exist!' });
    }

    return res.status(200).json({ existUnternehmen });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const update = async (req: Request, res: Response) => {
  try {
    const id =
      (req?.params?.unternehmenId && +req?.params?.unternehmenId) || null;
    const {
      title,
      titleGe,
      titleFr,
      description,
      descriptionGe,
      descriptionFr,
    } = req.body;

    if (!id) {
      return res.status(400).json({ message: 'Unternehmen id is required!' });
    }

    const existUnternehmen = await db.unternehmen.findFirst({
      where: { id, deleted: false },
    });

    if (!existUnternehmen) {
      return res.status(400).json({ message: 'Unternehmen not exist!' });
    }

    const update = await db.unternehmen.update({
      where: { id: existUnternehmen?.id },
      data: {
        title,
        titleGe,
        titleFr,
        description,
        descriptionGe,
        descriptionFr,
      },
    });
    return res
      .status(200)
      .json({ message: 'Unternehmen updated sucessfully!' });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const deleteUnternehmen = async (req: Request, res: Response) => {
  try {
    const id =
      (req?.params?.unternehmenId && +req?.params?.unternehmenId) || null;

    if (!id) {
      return res.status(400).json({ message: 'Unternehmen id is required!' });
    }

    const existUnternehmen = await db.unternehmen.findFirst({
      where: { id, deleted: false },
    });

    if (!existUnternehmen) {
      return res.status(400).json({ message: 'Unternehmen not exist!' });
    }

    const update = await db.unternehmen.update({
      where: { id: existUnternehmen?.id },
      data: { deleted: true },
    });
    return res
      .status(200)
      .json({ message: 'Unternehmen deleted sucessfully!' });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

export { getAllUnternehmen, create, getById, update, deleteUnternehmen };
