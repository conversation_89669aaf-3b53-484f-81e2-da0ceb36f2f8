import { Request, Response } from 'express';
import db from '../../db';

const getAllDatenschutz = async (req: Request, res: Response) => {
  try {
    const skip = (req?.query?.skip && +req?.query?.skip) || 0;
    const take = (req?.query?.take && +req?.query?.take) || 10;
    const search =
      typeof req?.query?.search === 'string' ? req?.query?.search : '';

    const count = await db.datenschutz.count({
      where: {
        deleted: false,
        OR: [
          { title: { contains: search, mode: 'insensitive' } },
          { titleFr: { contains: search, mode: 'insensitive' } },
          { titleGe: { contains: search, mode: 'insensitive' } },
        ],
      },
    });
    const Datenschutzs = await db.datenschutz.findMany({
      where: {
        deleted: false,
        OR: [
          { title: { contains: search, mode: 'insensitive' } },
          { titleFr: { contains: search, mode: 'insensitive' } },
          { titleGe: { contains: search, mode: 'insensitive' } },
        ],
      },

      skip,
      take,
    });

    return res.status(200).json({
      Datenschutzs,
      count,
      nextFrom: count >= take + skip ? take + skip : false,
    });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const create = async (req: Request, res: Response) => {
  try {
    const {
      title,
      titleGe,
      titleFr,
      description,
      descriptionGe,
      descriptionFr,
    }: {
      title: string;
      titleFr: string;
      titleGe: string;
      description: string;
      descriptionGe: string;
      descriptionFr: string;
    } = req.body;

    if (!title || !description) {
      return res
        .status(400)
        .json({ message: 'title and description are required!' });
    }

    await db.datenschutz.create({
      data: {
        title,
        titleGe,
        titleFr,
        description,
        descriptionGe,
        descriptionFr,
      },
    });
    return res
      .status(200)
      .json({ message: 'Datenschutz created sucessfully!' });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const getById = async (req: Request, res: Response) => {
  try {
    const id =
      (req?.params?.datenschutzId && +req?.params?.datenschutzId) || null;

    if (!id) {
      return res.status(400).json({ message: 'Datenschutz id is required!' });
    }

    const existDatenschutz = await db.datenschutz.findFirst({
      where: { id, deleted: false },
    });

    if (!existDatenschutz) {
      return res.status(400).json({ message: 'Datenschutz not exist!' });
    }

    return res.status(200).json({ existDatenschutz });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const update = async (req: Request, res: Response) => {
  try {
    const id =
      (req?.params?.datenschutzId && +req?.params?.datenschutzId) || null;
    const {
      title,
      titleGe,
      titleFr,
      description,
      descriptionGe,
      descriptionFr,
    } = req.body;

    if (!id) {
      return res.status(400).json({ message: 'Datenschutz id is required!' });
    }

    const existDatenschutz = await db.datenschutz.findFirst({
      where: { id, deleted: false },
    });

    if (!existDatenschutz) {
      return res.status(400).json({ message: 'Datenschutz not exist!' });
    }

    const update = await db.datenschutz.update({
      where: { id: existDatenschutz?.id },
      data: {
        title,
        titleGe,
        titleFr,
        description,
        descriptionGe,
        descriptionFr,
      },
    });
    return res
      .status(200)
      .json({ message: 'Datenschutz updated sucessfully!' });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const deleteDatenschutz = async (req: Request, res: Response) => {
  try {
    const id =
      (req?.params?.datenschutzId && +req?.params?.datenschutzId) || null;

    if (!id) {
      return res.status(400).json({ message: 'Datenschutz id is required!' });
    }

    const existDatenschutz = await db.datenschutz.findFirst({
      where: { id, deleted: false },
    });

    if (!existDatenschutz) {
      return res.status(400).json({ message: 'Datenschutz not exist!' });
    }

    const update = await db.datenschutz.update({
      where: { id: existDatenschutz?.id },
      data: { deleted: true },
    });
    return res
      .status(200)
      .json({ message: 'Datenschutz deleted sucessfully!' });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

export { getAllDatenschutz, create, getById, update, deleteDatenschutz };
