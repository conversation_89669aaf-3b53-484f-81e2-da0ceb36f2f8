import { Request, Response } from 'express';
import db from '../../db';
import { removeFiles } from '../../utils/file/file.utils';

const getAllProducts = async (req: Request, res: Response) => {
  try {
    const skip = (req?.query?.skip && +req?.query?.skip) || 0;
    const take = (req?.query?.take && +req?.query?.take) || 10;
    const search =
      typeof req?.query?.search === 'string' ? req?.query?.search : '';

    const count = await db.product.count({
      where: {
        deleted: false,
        OR: [
          { title: { contains: search, mode: 'insensitive' } },
          { titleFr: { contains: search, mode: 'insensitive' } },
          { titleGe: { contains: search, mode: 'insensitive' } },
        ],
      },
    });
    const products = await db.product.findMany({
      where: {
        deleted: false,
        OR: [
          { title: { contains: search, mode: 'insensitive' } },
          { titleFr: { contains: search, mode: 'insensitive' } },
          { titleGe: { contains: search, mode: 'insensitive' } },
          { Category: { name: { contains: search, mode: "insensitive" } } },
          { Category: { nameFr: { contains: search, mode: "insensitive" } } },
          { Category: { nameGe: { contains: search, mode: "insensitive" } } }
        ],
      },
      include: {
        Files: {
          where: { deleted: false },
          orderBy: [{ isMainImage: 'desc' }],
          select: { id: true, url: true, mimeType: true },
        },
        Pdf: { select: { id: true, url: true, mimeType: true } },
        Category: true,
        SubCategory: true,
        bigCategory: true,
      },
      orderBy: { productNumber: 'asc' },
      skip,
      take,
    });
    return res.status(200).json({
      products,
      count,
      nextFrom: count >= take + skip ? take + skip : false,
    });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const create = async (req: Request, res: Response) => {
  try {
    const Images: any = req?.files;
    const { files, file } = Images;
    const {
      title,
      titleGe,
      titleFr,
      description,
      descriptionGe,
      descriptionFr,
      moreDetail,
      moreDetailGe,
      moreDetailFr,
      categoryId,
      productNumber,
      imgAlt,
      subCategoryId,
      bigCategoryId,
    }: {
      title: string;
      titleGe: string;
      titleFr: string;
      description: string;
      descriptionGe: string;
      descriptionFr: string;
      imgAlt?: string;
      moreDetail: string;
      moreDetailGe: string;
      moreDetailFr: string;
      categoryId: number;
      productNumber: number;
      subCategoryId: number;
      bigCategoryId: number;
    } = req.body;

    if (!title || !description || !productNumber) {
      return res
        .status(400)
        .json({ message: 'title and description are required!' });
    }

    if (!files && files?.length == 0 && !file && file?.length == 0) {
      return res.status(400).json({ message: 'Files are required!' });
    }
    if (categoryId) {
      const existCategory = await db.category.findFirst({
        where: { id: +categoryId, deleted: false },
      });
      if (!existCategory) {
        return res.status(400).json({ message: 'Category not exist!' });
      }
    }

    const da = await db.product.create({
      data: {
        title,
        titleGe,
        titleFr,
        description,
        descriptionGe,
        descriptionFr,
        moreDetail,
        moreDetailGe,
        moreDetailFr,
        imgAlt,
        productNumber: +productNumber,
        ...(bigCategoryId && {
          bigCategory: { connect: { id: +bigCategoryId } },
        }),
        ...(categoryId && { Category: { connect: { id: +categoryId } } }),
        ...(subCategoryId && {
          SubCategory: { connect: { id: +subCategoryId } },
        }),
        ...(files &&
          files.length > 0 && {
          Files: {
            create: files.map((file: any, index: number) => ({
              url: file.path,
              mimeType: file.mimetype,
              isMainImage: index === 0,
            })),
          },
        }),
        ...(file &&
          file.length > 0 && {
          Pdf: {
            create: {
              url: file[0].path,
              mimeType: file[0].mimetype,
            },
          },
        }),
      },
    });

    return res.status(200).json({ message: 'Product created successfully!' });
  } catch (error) {
    console.log('error>>>', error);
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const getByCategoryId = async (req: Request, res: Response) => {
  try {
    const skip = (req?.query?.skip && +req?.query?.skip) || 0;
    const take = (req?.query?.take && +req?.query?.take) || 10;
    const search =
      typeof req?.query?.search === 'string' ? req?.query?.search : '';
    const categoryId =
      (req?.query?.categoryId && +req?.query?.categoryId) || null;

    const subCategoryId =
      (req?.query?.subCategoryId && +req?.query?.subCategoryId) || null;

    const bigCategoryId =
      (req?.query?.bigCategoryId && +req?.query?.bigCategoryId) || null;

    const count = await db.product.count({
      where: {
        ...(categoryId && { categoryId: +categoryId }),
        ...(subCategoryId && { subCategoryId: +subCategoryId }),
        ...(bigCategoryId && { bigCategoryId: +bigCategoryId }),
        deleted: false,
        OR: [
          { title: { contains: search, mode: 'insensitive' } },
          { titleFr: { contains: search, mode: 'insensitive' } },
          { titleGe: { contains: search, mode: 'insensitive' } },
        ],
      },
    });

    const existCategory = await db.product.findMany({
      where: {
        ...(categoryId && { categoryId: +categoryId }),
        ...(subCategoryId && { subCategoryId: +subCategoryId }),
        ...(bigCategoryId && { bigCategoryId: +bigCategoryId }),
        deleted: false,
        OR: [
          { title: { contains: search, mode: 'insensitive' } },
          { titleFr: { contains: search, mode: 'insensitive' } },
          { titleGe: { contains: search, mode: 'insensitive' } },
        ],
      },
      orderBy: { productNumber: 'asc' },
      include: {
        Files: {
          where: { deleted: false },
          orderBy: [{ isMainImage: 'desc' }],
          select: { id: true, url: true, mimeType: true },
        },
        Pdf: { select: { id: true, url: true, mimeType: true } },
        Category: true,
      },
      skip,
      take,
    });

    if (!existCategory) {
      return res.status(400).json({ message: 'Category not exist!' });
    }

    return res.status(200).json({
      existCategory,
      count,
      nextFrom: count >= take + skip ? take + skip : false,
    });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const getById = async (req: Request, res: Response) => {
  try {
    const id = (req?.params?.productId && +req?.params?.productId) || null;

    if (!id) {
      return res.status(400).json({ message: 'Product id is required!' });
    }

    const existProduct = await db.product.findFirst({
      where: { id, deleted: false },
      include: {
        Category: { where: { deleted: false } },
        SubCategory: { where: { deleted: false } },
        bigCategory: { where: { deleted: false } },
        Files: {
          where: { deleted: false },
          orderBy: [{ isMainImage: 'desc' }],
          select: { id: true, url: true, mimeType: true, isMainImage: true },
        },
        Pdf: { select: { id: true, url: true, mimeType: true } },
      },
    });

    if (!existProduct) {
      return res.status(400).json({ message: 'Product not exist!' });
    }

    return res.status(200).json({ existProduct });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const update = async (req: Request, res: Response) => {
  try {
    const Images: any = req?.files;
    const { files, file, mainImage } = Images;
    const id = (req?.params?.productId && +req?.params?.productId) || null;
    const {
      title,
      titleGe,
      titleFr,
      description,
      descriptionGe,
      descriptionFr,
      moreDetail,
      moreDetailGe,
      moreDetailFr,
      categoryId,
      productNumber,
      imgAlt,
      subCategoryId,
      bigCategoryId,
    } = req.body;
    let uploadImages: any[] = [];

    if (files && files.length > 0) {
      if (mainImage && mainImage.length > 0) {
        files.push(mainImage[0]);
      }
      uploadImages = files;
    }

    if (!id) {
      return res.status(400).json({ message: 'Product id is required!' });
    }

    const existProduct = await db.product.findFirst({
      where: { id: id, deleted: false },
    });

    if (!existProduct) {
      return res.status(400).json({ message: 'Product not exist!' });
    }
    if (categoryId) {
      const existCategory = await db.category.findFirst({
        where: { id: +categoryId, deleted: false },
      });
      if (!existCategory) {
        return res.status(400).json({ message: 'Category not exist!' });
      }
    }
    if (subCategoryId) {
      const existSubCategory = await db.subCategory.findFirst({
        where: { id: +subCategoryId, deleted: false },
      });
      if (!existSubCategory) {
        return res.status(400).json({ message: 'Sub Category not exist!' });
      }
    }

    await db.product.update({
      where: { id: existProduct?.id },
      data: {
        title,
        titleGe,
        titleFr,
        description,
        descriptionGe,
        descriptionFr,
        moreDetail,
        moreDetailGe,
        moreDetailFr,
        ...(!bigCategoryId &&
          existProduct?.bigCategoryId && {
          bigCategory: { disconnect: { id: existProduct?.bigCategoryId } },
        }),
        ...(!categoryId &&
          existProduct?.categoryId && {
          Category: { disconnect: { id: existProduct?.categoryId } },
        }),
        ...(!subCategoryId &&
          existProduct?.subCategoryId && {
          SubCategory: { disconnect: { id: existProduct?.subCategoryId } },
        }),
        ...(bigCategoryId && {
          bigCategory: { connect: { id: +bigCategoryId } },
        }),
        ...(categoryId && { Category: { connect: { id: +categoryId } } }),
        ...(subCategoryId && {
          SubCategory: { connect: { id: +subCategoryId } },
        }),
        productNumber: +productNumber,
        imgAlt,
        ...(uploadImages && uploadImages?.length > 0
          ? {
            Files: {
              create: uploadImages?.map((file: any) => ({
                url: file?.path,
                mimeType: file?.mimetype,
                isMainImage: file?.fieldname === 'mainImage' ? true : false,
              })),
            },
          }
          : {
            ...(mainImage &&
              mainImage.length > 0 && {
              Files: {
                create: mainImage?.map((file: any) => ({
                  url: file?.path,
                  mimeType: file?.mimetype,
                  isMainImage:
                    file?.fieldname === 'mainImage' ? true : false,
                })),
              },
            }),
          }),
        ...(file &&
          file.length > 0 && {
          Pdf: {
            create: {
              url: file[0].path,
              mimeType: file[0].mimetype,
            },
          },
        }),
      },
    });

    return res.status(200).json({
      message: 'Product Update successfully!',
    });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const deleteProduct = async (req: Request, res: Response) => {
  try {
    const id = (req?.params?.productId && +req?.params?.productId) || null;

    if (!id) {
      return res.status(400).json({ message: 'Product id is required!' });
    }

    const existProduct = await db.product.findFirst({
      where: { id, deleted: false },
      include: { Files: true, Pdf: true },
    });

    if (!existProduct) {
      return res.status(400).json({ message: 'Product not exist!' });
    }
    removeFiles(existProduct?.Pdf);
    existProduct?.Files?.map((file) => {
      removeFiles(file);
    });
    const update = await db.product.update({
      where: { id: existProduct?.id },
      data: { deleted: true },
    });
    return res.status(200).json({ message: 'Product deleted sucessfully!' });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

export {
  getAllProducts,
  create,
  getById,
  getByCategoryId,
  update,
  deleteProduct,
};
