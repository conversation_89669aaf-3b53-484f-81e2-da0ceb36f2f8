import { Request, Response } from 'express'
import db from '../../db'

const getAllImpressums = async (req: Request, res: Response) => {
  try {
    const impressum = await db.impressum.findMany({
      where: {
        deleted: false,
      },
    })

    return res.status(200).json({ impressum })
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error })
  }
}

const getImpressumById = async (req: Request, res: Response) => {
  try {
    const id = (req?.params?.impressumId && +req?.params?.impressumId) || null

    if (!id) {
      return res.status(400).json({ message: 'Impressum id is required!' })
    }

    const existImpressum = await db.impressum.findFirst({
      where: { id, deleted: false },
    })

    if (!existImpressum) {
      return res.status(400).json({ message: 'Impressum not exist!' })
    }

    return res.status(200).json({ existImpressum })
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error })
  }
}

const updateImpressum = async (req: Request, res: Response) => {
  try {
    const id = req?.params?.impressumId && +req?.params?.impressumId

    if (!id) {
      return res.status(400).json({ message: 'Impressum Id is required' })
    }

    const {
      description,
      descriptionGe,
      descriptionFr,
    }: { description: string; descriptionGe: string; descriptionFr: string } =
      req.body

    const existImpressum = await db.impressum.findFirst({
      where: { id, deleted: false },
    })

    if (!existImpressum) {
      return res.status(400).json({ message: 'Impressum not exist!' })
    }

    await db.impressum.update({
      where: { id: existImpressum?.id },
      data: {
        description,
        descriptionGe,
        descriptionFr,
      },
    })
    return res.status(200).json({ message: 'Impressum updated sucessfully!' })
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error })
  }
}

export { getAllImpressums, updateImpressum, getImpressumById }
