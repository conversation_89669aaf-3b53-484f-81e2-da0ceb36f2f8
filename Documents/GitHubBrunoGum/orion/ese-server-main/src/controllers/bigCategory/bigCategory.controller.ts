import { Request, Response } from 'express';
import db from '../../db';
import { removeFiles } from '../../utils/file/file.utils';

const getBigAllCategories = async (req: Request, res: Response) => {
  try {
    const skip = (req?.query?.skip && +req?.query?.skip) || 0;
    const take = (req?.query?.take && +req?.query?.take) || undefined;
    const search =
      typeof req?.query?.search === 'string' ? req?.query?.search : '';
    const count = await db.bigCategory.count({
      where: {
        deleted: false,
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { nameGe: { contains: search, mode: 'insensitive' } },
          { nameFr: { contains: search, mode: 'insensitive' } },
        ],
      },
    });
    const categories = await db.bigCategory.findMany({
      where: {
        deleted: false,
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { nameGe: { contains: search, mode: 'insensitive' } },
          { nameFr: { contains: search, mode: 'insensitive' } },
        ],
      },
      orderBy: { bigCategoryNumber: 'asc' },
      include: {
        Product: {
          orderBy: { productNumber: 'asc' },
          include: { Pdf: true, Files: true },
        },
        File: { select: { id: true, url: true, mimeType: true } },
      },
      skip,
      take,
    });

    return res.status(200).json({
      categories,
      count,
      ...(take && { nextFrom: count >= take + skip ? take + skip : false }),
    });
  } catch (error) {
    console.log('error in change password', error);
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const createBigCategory = async (req: Request, res: Response) => {
  try {
    const { name, nameGe, nameFr, bigCategoryNumber } = req.body;
    const file: Express.Multer.File | undefined =
      req.file as Express.Multer.File;
    if (!name) {
      return res.status(400).json({ message: 'Category name is required!' });
    }
    const existCategory = await db.bigCategory.findFirst({
      where: { deleted: false, OR: [{ name }, { nameGe }, { nameFr }] },
    });
    if (existCategory) {
      return res.status(400).json({ message: 'Category already exist!' });
    }
    const create = await db.bigCategory.create({
      data: {
        name,
        nameGe,
        nameFr,
        bigCategoryNumber: +bigCategoryNumber,
        File: {
          create: { url: file?.path, mimeType: file?.mimetype },
        },
      },
    });
    return res
      .status(200)
      .json({ message: ' Big Category created sucessfully!' });
  } catch (error) {
    console.log('error in change password', error);
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const getByIdBigCategory = async (req: Request, res: Response) => {
  try {
    const id =
      (req?.params?.bigCatedoryId && +req?.params?.bigCatedoryId) || null;

    if (!id) {
      return res.status(400).json({ message: ' big Category id is required!' });
    }

    const existCategory = await db.bigCategory.findFirst({
      where: { id, deleted: false },
      include: {
        Product: {
          include: { Pdf: true, Files: true },
        },
        File: { select: { id: true, url: true, mimeType: true } },
      },
    });

    if (!existCategory) {
      return res.status(400).json({ message: 'Category not exist!' });
    }

    return res.status(200).json({ existCategory });
  } catch (error) {
    console.log('error in change password', error);
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const updateBigCategory = async (req: Request, res: Response) => {
  try {
    const id =
      (req?.params?.bigCatedoryId && +req?.params?.bigCatedoryId) || null;
    const { name, nameGe, nameFr, bigCategoryNumber } = req.body;

    if (!id) {
      return res.status(400).json({ message: 'Category id is required!' });
    }
    const sameCategory = await db.bigCategory.findFirst({
      where: { OR: [{ name }, { nameGe }, { nameFr }], deleted: false },
    });

    const existCategory = await db.bigCategory.findFirst({
      where: { id, deleted: false },
    });

    // console.log("existCategory-->",existCategory)
    if (
      existCategory?.id &&
      sameCategory?.id &&
      sameCategory?.id !== existCategory?.id
    ) {
      return res
        .status(400)
        .json({ message: 'Category with this name already exist!' });
    }

    if (!existCategory) {
      return res.status(400).json({ message: 'Category not exist!' });
    }

    await db.bigCategory.update({
      where: { id: existCategory?.id },
      data: {
        name,
        nameGe,
        nameFr,
        bigCategoryNumber: +bigCategoryNumber,
      },
    });
    return res.status(200).json({ message: 'Category updated sucessfully!' });
  } catch (error) {
    console.log('error in change password', error);
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const deleteBigCategory = async (req: Request, res: Response) => {
  try {
    const id =
      (req?.params?.bigCatedoryId && +req?.params?.bigCatedoryId) || null;

    if (!id) {
      return res.status(400).json({ message: ' Big Category id is required!' });
    }
    console.log('id', id);

    const existCategory = await db.bigCategory.findFirst({
      where: { id: +id, deleted: false },
      include: { Product: { where: { deleted: false } }, File: true },
    });

    if (existCategory?.Product && existCategory?.Product?.length > 0) {
      return res.status(400).json({ message: 'This Category have Products!' });
    }
    console.log('existCategory', existCategory);
    if (!existCategory) {
      return res.status(400).json({ message: 'big  Category not exist!' });
    }
    removeFiles(existCategory?.File);

    const update = await db.bigCategory.update({
      where: { id: existCategory?.id },
      data: { deleted: true, File: { update: { deleted: true } } },
    });
    return res.status(200).json({ message: 'Category deleted sucessfully!' });
  } catch (error) {
    console.log('error in change password', error);
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

export {
  getBigAllCategories,
  createBigCategory,
  getByIdBigCategory,
  updateBigCategory,
  deleteBigCategory,
};
