import { Request, Response } from 'express';
import db from '../../db';
import { removeFiles } from '../../utils/file/file.utils';

const getAllManuals = async (req: Request, res: Response) => {
  try {
    const skip = (req?.query?.skip && +req?.query?.skip) || 0;
    const take = (req?.query?.take && +req?.query?.take) || 10;
    const search =
      typeof req?.query?.search === 'string' ? req?.query?.search : '';

    const count = await db.manual.count({
      where: {
        deleted: false,
        OR: [
          { title: { contains: search, mode: 'insensitive' } },
          { titleFr: { contains: search, mode: 'insensitive' } },
          { titleGe: { contains: search, mode: 'insensitive' } },
        ],
      },
    });
    const manuals = await db.manual.findMany({
      where: {
        deleted: false,
        OR: [
          { title: { contains: search, mode: 'insensitive' } },
          { titleFr: { contains: search, mode: 'insensitive' } },
          { titleGe: { contains: search, mode: 'insensitive' } },
        ],
      },
      include: {
        File: { select: { id: true, url: true, mimeType: true } },
      },
      skip,
      take,
    });

    return res.status(200).json({
      manuals,
      count,
      nextFrom: count >= take + skip ? take + skip : false,
    });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const create = async (req: Request, res: Response) => {
  try {
    const file: Express.Multer.File | undefined =
      req.file as Express.Multer.File;

    const {
      title,
      titleGe,
      titleFr,
      description,
      descriptionGe,
      descriptionFr,
    }: {
      title: string;
      titleFr: string;
      titleGe: string;
      description: string;
      descriptionGe: string;
      descriptionFr: string;
    } = req.body;

    if (!title || !description) {
      return res
        .status(400)
        .json({ message: 'title and description are required!' });
    }

    const create = await db.manual.create({
      data: {
        title,
        titleGe,
        titleFr,
        description,
        descriptionGe,
        descriptionFr,
        File: {
          create: { url: file?.path, mimeType: file?.mimetype },
        },
      },
      include: { File: { select: { id: true, url: true, mimeType: true } } },
    });
    return res.status(200).json({ message: 'Manual created sucessfully!' });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const getById = async (req: Request, res: Response) => {
  try {
    const id = (req?.params?.manualId && +req?.params?.manualId) || null;

    if (!id) {
      return res.status(400).json({ message: 'Manual id is required!' });
    }

    const existManual = await db.manual.findFirst({
      where: { id, deleted: false },
      include: {
        File: { select: { id: true, url: true, mimeType: true } },
      },
    });

    if (!existManual) {
      return res.status(400).json({ message: 'Manual not exist!' });
    }

    return res.status(200).json({ existManual });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const update = async (req: Request, res: Response) => {
  try {
    const id = (req?.params?.manualId && +req?.params?.manualId) || null;
    const {
      title,
      titleGe,
      titleFr,
      description,
      descriptionGe,
      descriptionFr,
    } = req.body;

    if (!id) {
      return res.status(400).json({ message: 'Manual id is required!' });
    }

    const existManual = await db.manual.findFirst({
      where: { id, deleted: false },
    });

    if (!existManual) {
      return res.status(400).json({ message: 'Manual not exist!' });
    }

    const update = await db.manual.update({
      where: { id: existManual?.id },
      data: {
        title,
        titleGe,
        titleFr,
        description,
        descriptionGe,
        descriptionFr,
      },
    });
    return res.status(200).json({ message: 'Manual updated sucessfully!' });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

const deleteManual = async (req: Request, res: Response) => {
  try {
    const id = (req?.params?.manualId && +req?.params?.manualId) || null;

    if (!id) {
      return res.status(400).json({ message: 'Manual id is required!' });
    }

    const existManual = await db.manual.findFirst({
      where: { id, deleted: false },
      include: { File: true },
    });

    if (!existManual) {
      return res.status(400).json({ message: 'Manual not exist!' });
    }
    existManual?.File.id && removeFiles(existManual?.File);

    const update = await db.manual.update({
      where: { id: existManual?.id },
      data: { deleted: true },
    });
    return res.status(200).json({ message: 'Manual deleted sucessfully!' });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error!', error });
  }
};

export { getAllManuals, create, getById, update, deleteManual };
