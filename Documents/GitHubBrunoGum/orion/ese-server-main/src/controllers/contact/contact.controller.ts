import { Request, Response } from 'express';
import nodemailer from 'nodemailer';

const transporter = nodemailer.createTransport({
  host: 'smtp.websupport.sk',
  port: 465,
  secure: true,
  auth: {
    user: '<EMAIL>',
    pass: 'Tve8kK#u(.SfH,k|4x*T',
  },
});

export const contactMail = async (req: Request, res: Response) => {
  try {
    const { name, email, phone, news } = req.body;

    const requiredFields = ['name', 'email', 'phone', 'news'];

    for (const field of requiredFields) {
      if (!req.body[field]) {
        return res
          .status(404)
          .json({ message: `${field} is missing in the request body!` });
      }
    }

    const mailOptions = {
      from: email,
      to: ['<EMAIL>'],
      subject: `Message from ${name}`,
      html: `<h1>Contact Us<br> </h1><b>Name and surname</b> : ${name} <br/> <b>E-mail </b> : ${email} <br> <b>Phone contact </b> : ${phone} <br> <b>News </b> : ${news} <br>`,
    };

    transporter.sendMail(mailOptions, function (err: any, info: any) {
      if (err) {
        return res.status(500).json({ message: err });
      }
      return res.status(200).json({ message: info.response });
    });
  } catch (error) {
    console.log('error', error);
    return res.status(500).json({ message: 'Internal Server Error!', error });
  }
};
