import express from 'express';
import authMiddleware from '../../middlewares/auth.middleware';
import { UserRole } from '@prisma/client';
import {
  getAllUnternehmen,
  create,
  getById,
  update,
  deleteUnternehmen,
} from '../../controllers/unternehmen/unternehmen.controller';

const router = express.Router();

router.get('/', getAllUnternehmen);
router.post('/', authMiddleware([UserRole.ADMIN]), create);
router.patch('/:unternehmenId', authMiddleware([UserRole.ADMIN]), update);
router.get('/:unternehmenId', getById);
router.delete(
  '/:unternehmenId',
  authMiddleware([UserRole.ADMIN]),
  deleteUnternehmen
);

export default router;
