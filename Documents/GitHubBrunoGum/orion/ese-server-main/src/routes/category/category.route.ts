import express from 'express';
import authMiddleware from '../../middlewares/auth.middleware';
import { UserRole } from '@prisma/client';
import {
  create,
  deleteCategory,
  getAllCategories,
  getAllCategoriesOrProduct,
  getById,
  update,
} from '../../controllers/category/category.controller';
import multerUploadMiddleware from '../../services/multer/allowedFile.service';
import {
  deleteSubCategory,
  getAllSubCategories,
  getByIdSubCategory,
  subCategoryCreate,
  updateSubCategory,
} from '../../controllers/subCategory/subCategory.controller';
import {
  createBigCategory,
  deleteBigCategory,
  getBigAllCategories,
  getByIdBigCategory,
  updateBigCategory,
} from '../../controllers/bigCategory/bigCategory.controller';

const router = express.Router();

router.get('/', getAllCategories);
router.get('/big-category', getBigAllCategories);
router.get('/sub-category', getAllSubCategories);
router.get('/product-category', getAllCategoriesOrProduct);

router.post(
  '/big-category',
  multerUploadMiddleware(),
  authMiddleware([UserRole.ADMIN]),
  createBigCategory
);

router.post(
  '/',
  multerUploadMiddleware(),
  authMiddleware([UserRole.ADMIN]),
  create
);

router.post(
  '/sub-category',
  multerUploadMiddleware(),
  authMiddleware([UserRole.ADMIN]),
  subCategoryCreate
);
router.patch(
  '/big-category/:bigCatedoryId',
  authMiddleware([UserRole.ADMIN]),
  updateBigCategory
);
router.patch('/:catedoryId', authMiddleware([UserRole.ADMIN]), update);
router.patch(
  '/sub-category/:subCategoryId',
  authMiddleware([UserRole.ADMIN]),
  updateSubCategory
);
router.get('/:catedoryId', getById);
router.get('/big-category/:bigCatedoryId', getByIdBigCategory);
router.get('/sub-category/:subCategoryId', getByIdSubCategory);
router.delete(
  '/sub-category/:subCategoryId',
  authMiddleware([UserRole.ADMIN]),
  deleteSubCategory
);

router.delete(
  '/big-category/:bigCatedoryId',
  authMiddleware([UserRole.ADMIN]),
  deleteBigCategory
);
router.delete('/:catedoryId', authMiddleware([UserRole.ADMIN]), deleteCategory);

export default router;
