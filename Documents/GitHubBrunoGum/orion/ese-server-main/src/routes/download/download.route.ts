import express from 'express';
import authMiddleware from '../../middlewares/auth.middleware';
import { UserRole } from '@prisma/client';
import {
  create,
  deleteProduct,
  getAllDownloads,
  getById,
  update,
} from '../../controllers/download/download.controller';
import upload from '../../services/multer/multer.service';

const router = express.Router();

router.get('/', getAllDownloads);
router.post(
  '/',
  authMiddleware([UserRole.ADMIN]),
  upload.array('files'),
  create
);
router.patch(
  '/:downloadId',
  upload.array('files'),
  authMiddleware([UserRole.ADMIN]),
  update
);
router.get('/:downloadId', getById);
router.delete('/:downloadId', authMiddleware([UserRole.ADMIN]), deleteProduct);

export default router;
