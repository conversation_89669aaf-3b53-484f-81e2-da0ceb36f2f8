import { UserRole } from '@prisma/client';
import {
  fileUpdate,
  fileDelete,
  File,
} from '../../controllers/file/file.controller';
import authMiddleware from '../../middlewares/auth.middleware';
import multerUploadMiddleware from '../../services/multer/allowedFile.service';
const router = require('express').Router();

router.patch(
  '/:fileId',
  multerUploadMiddleware(),
  authMiddleware([UserRole.ADMIN]),
  fileUpdate
);
router.delete('/:fileId', authMiddleware([UserRole.ADMIN]), fileDelete);
router.post('/', multerUploadMiddleware(), File);

export default router;
