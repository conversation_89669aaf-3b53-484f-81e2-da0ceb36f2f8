import express from 'express';
import authMiddleware from '../../middlewares/auth.middleware';
import { UserRole } from '@prisma/client';
import {
  create,
  deleteProduct,
  getAllProducts,
  getById,
  update,
  getByCategoryId,
} from '../../controllers/product/product.controller';
import upload from '../../services/multer/multer.service';

const router = express.Router();
//
router.get('/', getAllProducts);
router.post(
  '/',
  authMiddleware([UserRole.ADMIN]),
  upload.fields([{ name: 'file', maxCount: 1 }, { name: 'files' }]),
  create
);
router.patch(
  '/:productId',
  upload.fields([
    { name: 'file', maxCount: 1 },
    { name: 'mainImage', maxCount: 1 },
    { name: 'files' },
  ]),
  authMiddleware([UserRole.ADMIN]),
  update
);
router.get('/by-Category', getByCategoryId);
router.get('/:productId', getById);
router.delete('/:productId', authMiddleware([UserRole.ADMIN]), deleteProduct);

export default router;
