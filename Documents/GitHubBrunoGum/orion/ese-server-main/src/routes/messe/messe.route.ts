import express from 'express';
import authMiddleware from '../../middlewares/auth.middleware';
import { UserRole } from '@prisma/client';
import {
  create,
  deleteMesse,
  getAllMesse,
  getById,
  update,
} from '../../controllers/messe/messe.controller';
import upload from '../../services/multer/multer.service';
import multerUploadMiddleware from '../../services/multer/allowedFile.service';

const router = express.Router();

router.get('/', getAllMesse);
router.post(
  '/',
  multerUploadMiddleware(),
  authMiddleware([UserRole.ADMIN]),
  // upload.single('file'),
  create
);
router.patch('/:messeId', authMiddleware([UserRole.ADMIN]), update);
router.get('/:messeId', getById);
router.delete('/:messeId', authMiddleware([UserRole.ADMIN]), deleteMesse);

export default router;
