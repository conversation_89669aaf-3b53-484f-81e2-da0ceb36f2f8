import express from 'express'
import authMiddleware from '../../middlewares/auth.middleware'
import { UserRole } from '@prisma/client'
import {
  getAllImpressums,
  getImpressumById,
  updateImpressum,
} from '../../controllers/impressum/impressum.controller'

const router = express.Router()

router.get('/', getAllImpressums)
router.patch('/:impressumId', authMiddleware([UserRole.ADMIN]), updateImpressum)
router.get('/:impressumId', getImpressumById)

export default router
