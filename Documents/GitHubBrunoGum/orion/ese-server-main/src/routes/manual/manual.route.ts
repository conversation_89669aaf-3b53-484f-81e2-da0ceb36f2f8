import express from 'express';
import authMiddleware from '../../middlewares/auth.middleware';
import { UserRole } from '@prisma/client';
import {
  create,
  deleteManual,
  getAllManuals,
  getById,
  update,
} from '../../controllers/manual/manual.controller';
import upload from '../../services/multer/multer.service';
import multerUploadMiddleware from '../../services/multer/allowedFile.service';

const router = express.Router();

router.get('/', getAllManuals);
router.post(
  '/',
  multerUploadMiddleware(),
  authMiddleware([UserRole.ADMIN]),
  // upload.single('file'),
  create
);
router.patch('/:manualId', authMiddleware([UserRole.ADMIN]), update);
router.get('/:manualId', getById);
router.delete('/:manualId', authMiddleware([UserRole.ADMIN]), deleteManual);

export default router;
