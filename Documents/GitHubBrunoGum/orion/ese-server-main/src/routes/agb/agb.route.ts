import express from 'express';
import authMiddleware from '../../middlewares/auth.middleware';
import { UserRole } from '@prisma/client';
import {
  getAllAgb,
  create,
  getById,
  update,
  deleteAgb,
} from '../../controllers/agb/agb.controller';

const router = express.Router();

router.get('/', getAllAgb);
router.post('/', authMiddleware([UserRole.ADMIN]), create);
router.patch('/:agbId', authMiddleware([UserRole.ADMIN]), update);
router.get('/:agbId', getById);
router.delete('/:agbId', authMiddleware([UserRole.ADMIN]), deleteAgb);

export default router;
