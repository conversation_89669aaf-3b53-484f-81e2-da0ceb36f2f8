import express from 'express';
import authMiddleware from '../../middlewares/auth.middleware';
import { UserRole } from '@prisma/client';
import { csVUploader } from '../../controllers/csvUploader/csvUploader.controller';
import multerUploadMiddleware from '../../services/multer/allowedFile.service';

const router = express.Router();

router.post(
  '/',
  // authMiddleware([UserRole.ADMIN]),
  // upload.fields([{ name: 'file', maxCount: 1 }, { name: 'files' }]),
  multerUploadMiddleware(),
  csVUploader
);

export default router;
