import express from 'express';
import authMiddleware from '../../middlewares/auth.middleware';
import { UserRole } from '@prisma/client';
import {
  getAllDatenschutz,
  create,
  getById,
  update,
  deleteDatenschutz,
} from '../../controllers/datenschutz/datenschutz.controller';

const router = express.Router();

router.get('/', getAllDatenschutz);
router.post('/', authMiddleware([UserRole.ADMIN]), create);
router.patch('/:datenschutzId', authMiddleware([UserRole.ADMIN]), update);
router.get('/:datenschutzId', getById);
router.delete(
  '/:datenschutzId',
  authMiddleware([UserRole.ADMIN]),
  deleteDatenschutz
);

export default router;
