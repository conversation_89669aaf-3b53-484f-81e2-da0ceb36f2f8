import jwt, { JwtPayload } from 'jsonwebtoken';
import db from '../db';
import { Request, Response, NextFunction } from 'express';
import { UserRole } from '@prisma/client';

const authMiddleware =
  (allowedRoles: UserRole[]) =>
  async (req: Request, res: Response, next: NextFunction) => {
    const token = req.header('auth-token');

    if (!token) return res.status(401).json({ message: 'Access denied' });

    try {
      const decoded: JwtPayload | string = jwt.verify(
        token,
        process.env.TOKEN_SECRET as string
      );
      // Handle the case where `decoded` is a string (e.g., when the token is invalid)
      if (typeof decoded === 'string') {
        return res.status(400).json({ message: 'invalid token!' });
      }

      const { id } = decoded;

      const validUser = await db.user.findFirst({
        where: {
          id,
          deleted: false,
        },
      });

      if (!validUser) {
        return res.status(403).json({ message: 'Invalid user!' });
      }

      if (validUser.deleted) {
        return res.status(403).json({ message: 'user not exists' });
      }

      (req as any).userId = id;
      return allowedRoles.includes(validUser.role)
        ? next()
        : res.status(403).json({ message: 'Access denied!' });
    } catch (err) {
      return res.status(400).json({ message: 'invalid token!' });
    }
  };

export default authMiddleware;
