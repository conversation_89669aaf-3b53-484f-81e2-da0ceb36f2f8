import { check } from 'express-validator';

const createUserValidationRules = [
  check('username').not().isEmpty().withMessage('Username is required'),
  check('email')
    .isEmail()
    .optional({ checkFalsy: true })
    .withMessage('Invalid email address'),
  check('role').not().isEmpty().withMessage('Role is required'),
  check('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long'),
];

const userNameValidationRules = [
  check('username').not().isEmpty().withMessage('Username is required'),
];

const loginUserValidationRules = [
  check('username').not().isEmpty().withMessage('Username is required'),
  check('password').isLength({ min: 6 }).withMessage('Password is required!'),
];

export {
  createUserValidationRules,
  loginUserValidationRules,
  userNameValidationRules,
};
