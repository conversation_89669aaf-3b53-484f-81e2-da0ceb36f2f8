import { NextFunction, Response, Request } from 'express';
import { validationResult } from 'express-validator';

const validate = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (errors.isEmpty()) {
    return next(); // No validation errors, proceed to the route handler
  }

  return res.status(400).json({ errors: errors.array() });
};

export { validate };
