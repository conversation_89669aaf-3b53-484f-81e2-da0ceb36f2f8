import 'dotenv/config';
import CryptoJS from 'crypto-js';

function decryptPassword({ encryptedPassword }: { encryptedPassword: string }) {
  if (process.env.PASSWORD_SECRET_KEY) {
    
    try {
      const bytes = CryptoJS.AES.decrypt(
        encryptedPassword,
        process.env.PASSWORD_SECRET_KEY
      );
      const originalText = bytes.toString(CryptoJS.enc.Utf8); 
      return originalText;
    } catch (error) { 
      return false;
    }
  } 
  return false;
}

export default decryptPassword;
