const isoDatePattern = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z$/;

const isoFormDetector = ({ age }: { age: string }) => {
  if (!isoDatePattern.test(age)) {
    return false;
  } else {
    return true;
  }
};
const isIsoHiringDateFormat = ({ hiringDate }: { hiringDate: string }) => {
  if (!isoDatePattern.test(hiringDate)) {
    return false;
  } else {
    return true;
  }
};
const isIsoShiftTimeEndDateFormat = ({
  shiftTimeEnd,
}: {
  shiftTimeEnd: string;
}) => {
  if (!isoDatePattern.test(shiftTimeEnd)) {
    return false;
  } else {
    return true;
  }
};
const isIsoShiftTimeStartDateFormat = ({
  shiftTimeStart,
}: {
  shiftTimeStart: string;
}) => {
  if (!isoDatePattern.test(shiftTimeStart)) {
    return false;
  } else {
    return true;
  }
};

export {
  isoFormDetector,
  isIsoHiringDateFormat,
  isIsoShiftTimeEndDateFormat,
  isIsoShiftTimeStartDateFormat,
};
