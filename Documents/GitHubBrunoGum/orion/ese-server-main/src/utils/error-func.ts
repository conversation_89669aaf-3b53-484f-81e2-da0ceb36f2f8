import { Request, Response } from 'express'

const errorThrower = ({
  status,
  message,
  condition,
  res,
}: {
  status: number
  message: string
  condition: boolean
  res: Response
}) => {
  if (condition) return res.status(status).json({ message })
}

function checkMissingAttributesAndRespond({
  req,
  res,
  role,
  requiredAttributes,
  userRole,
}: {
  req: Request
  res: Response
  role: String
  requiredAttributes: string[]
  userRole: string
}) {
  const missingAttributes = requiredAttributes.filter((attr) => !req.body[attr])
  if (userRole === role && missingAttributes.length > 0) {
    // Store the response data, but don't send it immediately
    const responseData = {
      message: `Required attributes missing: ${missingAttributes.join(', ')}`,
    }
    return responseData
  }

  // Send a different response or do nothing if the condition is not met
}
export { errorThrower, checkMissingAttributesAndRespond }
