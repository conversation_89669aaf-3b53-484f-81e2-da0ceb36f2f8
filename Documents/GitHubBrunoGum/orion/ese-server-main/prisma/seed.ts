import db from '../src/db';
import { hashingPassword } from '../src/services/auth/auth.services';
import { UserRole } from '@prisma/client';
import {
  description,
  descriptionFr,
  descriptionGe,
} from '../src/utils/impressum';

/* Create First Admin User */
const createFirstAdmin = async () => {
  const username = 'admin';
  const email = process.env.FIRST_ADMIN_EMAIL;
  const password = process.env.FIRST_ADMIN_PASSWORD;

  if (
    !(await db.user.findFirst({
      where: {
        email,
        deleted: false,
      },
    }))
  ) {
    if (password && email) {
      const hashedPassword = await hashingPassword({ password });

      await db.user.create({
        data: {
          email,
          password: hashedPassword,
          role: UserRole.ADMIN,
          username,
        },
      });
      console.log(`Admin ${username} Created Sucessfully!`);
    } else {
      console.log(`Admin ${username} and password ${password} is required!`);
    }
  }
};

/* Create First Impressum */
const createFirstImpressum = async () => {
  const id = 1;

  if (
    !(await db.impressum.findFirst({
      where: {
        id,
        deleted: false,
      },
    }))
  ) {
    await db.impressum.create({
      data: {
        description: description,
        descriptionFr: descriptionFr,
        descriptionGe: descriptionGe,
      },
    });
  }
};

async function main() {
  await createFirstAdmin();
  await createFirstImpressum();
  console.log('seed!');
}

main()
  .then(async () => {
    await db.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await db.$disconnect();
    process.exit(1);
  });
