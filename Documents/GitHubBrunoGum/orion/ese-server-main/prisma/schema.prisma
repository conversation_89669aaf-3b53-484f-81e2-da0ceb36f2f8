generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  ADMIN
}

model User {
  id        Int      @id @default(autoincrement())
  username  String
  email     String?
  password  String
  role      UserRole
  deleted   <PERSON>ole<PERSON>  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model bigCategory {
  id                Int           @id @default(autoincrement())
  name              String
  nameGe            String?
  nameFr            String?
  bigCategoryNumber Float?
  Product           Product[]
  SubCategory       SubCategory[]
  File              File          @relation(fields: [fileId], references: [id])
  fileId            Int           @unique()
  deleted           Boolean       @default(false)
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt
}

model Category {
  id             Int           @id @default(autoincrement())
  name           String
  nameGe         String?
  nameFr         String?
  categoryNumber Float?
  Product        Product[]
  SubCategory    SubCategory[]
  deleted        Boolean       @default(false)
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
}

model SubCategory {
  id                Int          @id @default(autoincrement())
  name              String
  nameGe            String?
  nameFr            String?
  subCategoryNumber Float?
  Category          Category     @relation(fields: [categoryId], references: [id])
  categoryId        Int
  Product           Product[]
  deleted           Boolean      @default(false)
  createdAt         DateTime     @default(now())
  updatedAt         DateTime     @updatedAt
  bigCategory       bigCategory? @relation(fields: [bigCategoryId], references: [id])
  bigCategoryId     Int?
}

model Downloads {
  id        Int      @id @default(autoincrement())
  title     String
  titleGe   String?
  titleFr   String?
  Files     File[]
  deleted   Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Product {
  id            Int          @id @default(autoincrement())
  title         String
  titleGe       String?
  titleFr       String?
  description   String
  descriptionGe String?
  descriptionFr String?
  moreDetail    String?
  moreDetailGe  String?
  moreDetailFr  String?
  productNumber Float?
  imgAlt        String?
  Category      Category?    @relation(fields: [categoryId], references: [id])
  categoryId    Int?
  Pdf           File?        @relation(name: "file", fields: [fileId], references: [id])
  fileId        Int?         @unique
  Files         File[]       @relation(name: "Images")
  SubCategory   SubCategory? @relation(fields: [subCategoryId], references: [id])
  subCategoryId Int?
  deleted       Boolean      @default(false)
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  bigCategory   bigCategory? @relation(fields: [bigCategoryId], references: [id])
  bigCategoryId Int?
}

model Manual {
  id            Int      @id @default(autoincrement())
  title         String
  titleGe       String?
  titleFr       String?
  description   String
  descriptionGe String?
  descriptionFr String?
  File          File     @relation(fields: [fileId], references: [id])
  fileId        Int      @unique()
  deleted       Boolean  @default(false)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
}

model Unternehmen {
  id            Int      @id @default(autoincrement())
  title         String
  titleGe       String?
  titleFr       String?
  description   String
  descriptionGe String?
  descriptionFr String?
  deleted       Boolean  @default(false)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
}

model Agb {
  id            Int      @id @default(autoincrement())
  title         String
  titleGe       String?
  titleFr       String?
  description   String
  descriptionGe String?
  descriptionFr String?
  deleted       Boolean  @default(false)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
}

model Datenschutz {
  id            Int      @id @default(autoincrement())
  title         String
  titleGe       String?
  titleFr       String?
  description   String
  descriptionGe String?
  descriptionFr String?
  deleted       Boolean  @default(false)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
}

model Messe {
  id            Int      @id @default(autoincrement())
  title         String
  titleGe       String?
  titleFr       String?
  description   String
  descriptionGe String?
  descriptionFr String?
  deleted       Boolean  @default(false)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
}

model Impressum {
  id            Int      @id @default(autoincrement())
  description   String
  descriptionGe String?
  descriptionFr String?
  deleted       Boolean  @default(false)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
}

model File {
  id            Int          @id @default(autoincrement())
  url           String
  mimeType      String
  ProductImages Product?     @relation(name: "Images", fields: [imgeId], references: [id])
  imgeId        Int?
  File          Product?     @relation(name: "file")
  Manual        Manual?
  Downloads     Downloads?   @relation(fields: [downloadsId], references: [id])
  downloadsId   Int?
  deleted       Boolean      @default(false)
  isMainImage   Boolean?     @default(false)
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  bigCategory   bigCategory?
}
