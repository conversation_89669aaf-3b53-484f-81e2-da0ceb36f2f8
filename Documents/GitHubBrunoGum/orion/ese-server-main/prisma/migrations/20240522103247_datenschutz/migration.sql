-- CreateTable
CREATE TABLE "Unternehmen" (
    "id" SERIAL NOT NULL,
    "title" TEXT NOT NULL,
    "titleGe" TEXT,
    "titleFr" TEXT,
    "description" TEXT NOT NULL,
    "descriptionGe" TEXT,
    "descriptionFr" TEXT,
    "deleted" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Unternehmen_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Agb" (
    "id" SERIAL NOT NULL,
    "title" TEXT NOT NULL,
    "titleGe" TEXT,
    "titleFr" TEXT,
    "description" TEXT NOT NULL,
    "descriptionGe" TEXT,
    "descriptionFr" TEXT,
    "deleted" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Agb_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Datenschutz" (
    "id" SERIAL NOT NULL,
    "title" TEXT NOT NULL,
    "titleGe" TEXT,
    "titleFr" TEXT,
    "description" TEXT NOT NULL,
    "descriptionGe" TEXT,
    "descriptionFr" TEXT,
    "deleted" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Datenschutz_pkey" PRIMARY KEY ("id")
);
