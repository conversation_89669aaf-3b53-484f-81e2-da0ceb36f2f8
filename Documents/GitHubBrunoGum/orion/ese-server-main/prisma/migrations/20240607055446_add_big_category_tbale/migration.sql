/*
  Warnings:

  - You are about to drop the column `bigCategory` on the `Category` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "Category" DROP COLUMN "bigCategory";

-- AlterTable
ALTER TABLE "Product" ADD COLUMN     "bigCategoryId" INTEGER;

-- AlterTable
ALTER TABLE "SubCategory" ADD COLUMN     "bigCategoryId" INTEGER;

-- CreateTable
CREATE TABLE "bigCategory" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "nameGe" TEXT,
    "nameFr" TEXT,
    "bigCategoryNumber" DOUBLE PRECISION,
    "fileId" INTEGER NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "bigCategory_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "bigCategory_fileId_key" ON "bigCategory"("fileId");

-- AddForeignKey
ALTER TABLE "bigCategory" ADD CONSTRAINT "bigCategory_fileId_fkey" FOREIGN KEY ("fileId") REFERENCES "File"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SubCategory" ADD CONSTRAINT "SubCategory_bigCategoryId_fkey" FOREIGN KEY ("bigCategoryId") REFERENCES "bigCategory"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Product" ADD CONSTRAINT "Product_bigCategoryId_fkey" FOREIGN KEY ("bigCategoryId") REFERENCES "bigCategory"("id") ON DELETE SET NULL ON UPDATE CASCADE;
