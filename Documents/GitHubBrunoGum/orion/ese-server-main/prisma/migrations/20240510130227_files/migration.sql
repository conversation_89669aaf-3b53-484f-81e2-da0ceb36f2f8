/*
  Warnings:

  - You are about to drop the column `productId` on the `File` table. All the data in the column will be lost.
  - Made the column `fileId` on table `Product` required. This step will fail if there are existing NULL values in that column.

*/
-- DropForeignKey
ALTER TABLE "File" DROP CONSTRAINT "File_productId_fkey";

-- DropForeignKey
ALTER TABLE "Product" DROP CONSTRAINT "Product_fileId_fkey";

-- AlterTable
ALTER TABLE "File" DROP COLUMN "productId",
ADD COLUMN     "imgeId" INTEGER;

-- AlterTable
ALTER TABLE "Product" ALTER COLUMN "fileId" SET NOT NULL;

-- AddForeignKey
ALTER TABLE "Product" ADD CONSTRAINT "Product_fileId_fkey" FOREIGN KEY ("fileId") REFERENCES "File"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "File" ADD CONSTRAINT "File_imgeId_fkey" FOREIGN KEY ("imgeId") REFERENCES "Product"("id") ON DELETE SET NULL ON UPDATE CASCADE;
