{"name": "ese-server", "version": "1.0.0", "main": "index.js", "license": "MIT", "scripts": {"dev": "nodemon src/index.ts", "start": "ts-node src/index.ts", "db:reset": "npx prisma db push --force-reset", "db:migrate": "npx prisma migrate dev && npm run db:seed", "db:seed": "ts-node prisma/seed.ts"}, "dependencies": {"@prisma/client": "^5.15.0", "@types/file-type": "^10.9.1", "axios": "^1.7.2", "bcrypt": "^5.1.1", "bull-master": "^1.0.5", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.4.5", "exceljs": "^4.4.0", "express": "^4.18.2", "express-validator": "^7.0.1", "file-type": "^19.0.0", "fs": "^0.0.1-security", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.2", "moment-timezone": "^0.5.45", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.13", "ts-node": "^10.9.2", "xlsx": "^0.18.5"}, "devDependencies": {"@types/bcrypt": "^5.0.0", "@types/cors": "^2.8.14", "@types/crypto-js": "^4.2.2", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.3", "@types/morgan": "^1.9.6", "@types/multer": "^1.4.11", "@types/node": "^20.14.2", "@types/nodemailer": "^6.4.15", "@types/uuid": "^9.0.7", "eslint": "^8.51.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.28.1", "nodemon": "^3.0.1", "prisma": "^5.4.2", "typescript": "^5.4.5"}}